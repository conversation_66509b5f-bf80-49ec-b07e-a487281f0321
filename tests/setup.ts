import { beforeAll, afterAll, beforeEach } from 'vitest';
import { PrismaClient } from '@prisma/client';

// Set test environment
process.env.NODE_ENV = 'test';
process.env.DATABASE_URL = process.env.DATABASE_URL || 'postgresql://rsdh_bot:tahshoo4sal5Fael@127.0.0.1:15432/rsdh_test';
process.env.ADMIN_EMAIL = process.env.ADMIN_EMAIL || '<EMAIL>';
process.env.ADMIN_PASSWORD = process.env.ADMIN_PASSWORD || 'testpassword123';
process.env.TEST_EMAIL = process.env.TEST_EMAIL || '<EMAIL>';
process.env.TEST_PASSWORD = process.env.TEST_PASSWORD || 'testpassword123';
process.env.JWT_SECRET = process.env.JWT_SECRET || 'test-jwt-secret';

// Test database instance
export const testPrisma = new PrismaClient({
  datasources: {
    db: {
      url: process.env.DATABASE_URL,
    },
  },
});

let databaseAvailable = false;

beforeAll(async () => {
  try {
    // Try to connect to test database
    await testPrisma.$connect();
    databaseAvailable = true;

    // Clean up database at start
    await cleanDatabase();
  } catch (error) {
    console.warn('Database not available for tests:', error);
    databaseAvailable = false;
  }
});

afterAll(async () => {
  if (databaseAvailable) {
    try {
      // Clean up database at end
      await cleanDatabase();

      // Disconnect from test database
      await testPrisma.$disconnect();
    } catch (error) {
      console.warn('Error during test cleanup:', error);
    }
  }
});

beforeEach(async () => {
  if (databaseAvailable) {
    try {
      // Clean up database before each test
      await cleanDatabase();
    } catch (error) {
      console.warn('Error cleaning database before test:', error);
    }
  }
});

async function cleanDatabase() {
  if (!databaseAvailable) return;

  try {
    // Delete in correct order to avoid foreign key constraints
    await testPrisma.review.deleteMany();
    await testPrisma.appointment.deleteMany();
    await testPrisma.tutorAvailability.deleteMany();
    await testPrisma.tutorCareer.deleteMany();
    await testPrisma.tutorEducation.deleteMany();
    await testPrisma.tutorProfile.deleteMany();
    await testPrisma.verificationCode.deleteMany();
    await testPrisma.passwordResetToken.deleteMany();
    await testPrisma.refreshToken.deleteMany();
    await testPrisma.session.deleteMany();
    await testPrisma.account.deleteMany(); // Clean up accounts table
    await testPrisma.user.deleteMany();

    // Add a small delay to ensure cleanup is complete
    await new Promise(resolve => setTimeout(resolve, 10));
  } catch (error) {
    console.warn('Error cleaning database:', error);
    // If cleanup fails, try to continue anyway
  }
}

export { databaseAvailable };
