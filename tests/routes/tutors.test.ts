import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import { createApp } from '../../src/app';
import { prisma } from '../../src/lib/prisma';
import { generateSimpleToken } from '../../src/middleware/auth';
import { FastifyInstance } from 'fastify';

describe('Tutor Routes', () => {
  let app: FastifyInstance;
  let testUserId: string;
  let adminUserId: string;
  let testTutorId: string;
  let userToken: string;
  let adminToken: string;

  beforeEach(async () => {
    app = await createApp();

    // Create test user
    const testUser = await prisma.user.create({
      data: {
        email: `tutor-route-test-${Date.now()}@example.com`,
        name: 'Test User',
        password: 'hashedpassword',
        role: 'user'
      }
    });
    testUserId = testUser.id;
    userToken = generateSimpleToken(testUser.email);

    // Create admin user
    const adminUser = await prisma.user.create({
      data: {
        email: `admin-${Date.now()}@example.com`,
        name: 'Admin User',
        password: 'hashedpassword',
        role: 'admin'
      }
    });
    adminUserId = adminUser.id;
    adminToken = generateSimpleToken(adminUser.email);
  });

  afterEach(async () => {
    // Clean up test data
    if (testTutorId) {
      await prisma.tutorProfile.delete({
        where: { id: testTutorId }
      }).catch(() => {});
    }

    await prisma.user.deleteMany({
      where: {
        id: { in: [testUserId, adminUserId] }
      }
    }).catch(() => {});

    await app.close();
  });

  describe('POST /api/tutors/apply', () => {
    it('should create tutor application successfully', async () => {
      const applicationData = {
        title: 'Senior Software Engineer',
        bio: 'Experienced developer with 10+ years',
        rate: 100,
        education: [
          {
            degree: 'Bachelor of Science',
            fieldOfStudy: 'Computer Science',
            institution: 'MIT',
            startYear: 2010,
            endYear: 2014,
            description: 'CS fundamentals'
          }
        ],
        career: [
          {
            title: 'Senior Engineer',
            company: 'Tech Corp',
            startYear: 2018,
            current: true,
            description: 'Full-stack development'
          }
        ]
      };

      const response = await app.inject({
        method: 'POST',
        url: '/api/tutors/apply',
        headers: {
          authorization: `Bearer ${userToken}`
        },
        payload: applicationData
      });

      expect(response.statusCode).toBe(201);
      const result = JSON.parse(response.payload);
      expect(result.userId).toBe(testUserId);
      expect(result.title).toBe('Senior Software Engineer');
      expect(result.status).toBe('pending');

      testTutorId = result.id;
    });

    it('should return 400 if user already has tutor profile', async () => {
      // Create first application
      const applicationData = {
        title: 'Test Title',
        education: [
          {
            degree: 'Bachelor',
            fieldOfStudy: 'CS',
            institution: 'University',
            startYear: 2020
          }
        ],
        career: [
          {
            title: 'Developer',
            company: 'Company',
            startYear: 2022,
            current: true
          }
        ]
      };

      const firstResponse = await app.inject({
        method: 'POST',
        url: '/api/tutors/apply',
        headers: {
          authorization: `Bearer ${userToken}`
        },
        payload: applicationData
      });

      expect(firstResponse.statusCode).toBe(201);
      const firstResult = JSON.parse(firstResponse.payload);
      testTutorId = firstResult.id;

      // Try to create second application
      const secondResponse = await app.inject({
        method: 'POST',
        url: '/api/tutors/apply',
        headers: {
          authorization: `Bearer ${userToken}`
        },
        payload: applicationData
      });

      expect(secondResponse.statusCode).toBe(400);
      const result = JSON.parse(secondResponse.payload);
      expect(result.message).toBe('User already has a tutor profile');
    });

    it('should return 401 without authentication', async () => {
      const applicationData = {
        title: 'Test Title',
        education: [
          {
            degree: 'Bachelor',
            fieldOfStudy: 'CS',
            institution: 'University',
            startYear: 2020
          }
        ],
        career: [
          {
            title: 'Developer',
            company: 'Company',
            startYear: 2022,
            current: true
          }
        ]
      };

      const response = await app.inject({
        method: 'POST',
        url: '/api/tutors/apply',
        payload: applicationData
      });

      expect(response.statusCode).toBe(401);
    });

    it('should return 400 with invalid data', async () => {
      const invalidData = {
        title: 'Test Title',
        // Missing required education and career
      };

      const response = await app.inject({
        method: 'POST',
        url: '/api/tutors/apply',
        headers: {
          authorization: `Bearer ${userToken}`
        },
        payload: invalidData
      });

      expect(response.statusCode).toBe(400);
    });
  });

  describe('GET /api/tutors/profile', () => {
    beforeEach(async () => {
      // Create tutor profile for tests
      const applicationData = {
        title: 'Test Tutor',
        bio: 'Test bio',
        rate: 75,
        education: [
          {
            degree: 'Master',
            fieldOfStudy: 'Education',
            institution: 'University',
            startYear: 2015,
            endYear: 2017
          }
        ],
        career: [
          {
            title: 'Teacher',
            company: 'School',
            startYear: 2017,
            current: true
          }
        ]
      };

      const response = await app.inject({
        method: 'POST',
        url: '/api/tutors/apply',
        headers: {
          authorization: `Bearer ${userToken}`
        },
        payload: applicationData
      });

      const result = JSON.parse(response.payload);
      testTutorId = result.id;
    });

    it('should return tutor profile successfully', async () => {
      const response = await app.inject({
        method: 'GET',
        url: '/api/tutors/profile',
        headers: {
          authorization: `Bearer ${userToken}`
        }
      });

      expect(response.statusCode).toBe(200);
      const result = JSON.parse(response.payload);
      expect(result.id).toBe(testTutorId);
      expect(result.title).toBe('Test Tutor');
      expect(result.user).toBeDefined();
      expect(result.education).toHaveLength(1);
      expect(result.career).toHaveLength(1);
      expect(result.averageRating).toBe(0);
    });

    it('should return 404 if no tutor profile exists', async () => {
      // Create user without tutor profile
      const newUser = await prisma.user.create({
        data: {
          email: `no-tutor-${Date.now()}@example.com`,
          name: 'No Tutor User',
          password: 'hashedpassword'
        }
      });
      const newUserToken = generateSimpleToken(newUser.email);

      const response = await app.inject({
        method: 'GET',
        url: '/api/tutors/profile',
        headers: {
          authorization: `Bearer ${newUserToken}`
        }
      });

      expect(response.statusCode).toBe(404);
      const result = JSON.parse(response.payload);
      expect(result.message).toBe('Tutor profile not found');

      // Clean up
      await prisma.user.delete({ where: { id: newUser.id } });
    });

    it('should return 401 without authentication', async () => {
      const response = await app.inject({
        method: 'GET',
        url: '/api/tutors/profile'
      });

      expect(response.statusCode).toBe(401);
    });
  });

  describe('PUT /api/tutors/profile', () => {
    beforeEach(async () => {
      // Create tutor profile for tests
      const applicationData = {
        title: 'Original Title',
        bio: 'Original bio',
        rate: 50,
        education: [
          {
            degree: 'Bachelor',
            fieldOfStudy: 'CS',
            institution: 'University',
            startYear: 2020
          }
        ],
        career: [
          {
            title: 'Developer',
            company: 'Company',
            startYear: 2022,
            current: true
          }
        ]
      };

      const response = await app.inject({
        method: 'POST',
        url: '/api/tutors/apply',
        headers: {
          authorization: `Bearer ${userToken}`
        },
        payload: applicationData
      });

      const result = JSON.parse(response.payload);
      testTutorId = result.id;
    });

    it('should update tutor profile successfully', async () => {
      const updateData = {
        title: 'Updated Title',
        bio: 'Updated bio',
        rate: 100
      };

      const response = await app.inject({
        method: 'PUT',
        url: '/api/tutors/profile',
        headers: {
          authorization: `Bearer ${userToken}`
        },
        payload: updateData
      });

      expect(response.statusCode).toBe(200);
      const result = JSON.parse(response.payload);
      expect(result.title).toBe('Updated Title');
      expect(result.bio).toBe('Updated bio');
      expect(result.rate).toBe(100);
    });

    it('should return 404 if no tutor profile exists', async () => {
      // Create user without tutor profile
      const newUser = await prisma.user.create({
        data: {
          email: `no-tutor-update-${Date.now()}@example.com`,
          name: 'No Tutor User',
          password: 'hashedpassword'
        }
      });
      const newUserToken = generateSimpleToken(newUser.email);

      const updateData = {
        title: 'Updated Title'
      };

      const response = await app.inject({
        method: 'PUT',
        url: '/api/tutors/profile',
        headers: {
          authorization: `Bearer ${newUserToken}`
        },
        payload: updateData
      });

      expect(response.statusCode).toBe(404);

      // Clean up
      await prisma.user.delete({ where: { id: newUser.id } });
    });
  });
});
