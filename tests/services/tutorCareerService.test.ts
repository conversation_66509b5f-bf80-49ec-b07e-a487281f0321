import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import { prisma } from '../../src/lib/prisma';
import { TutorCareerService, CreateCareerData } from '../../src/services/tutorCareerService';

describe('TutorCareerService', () => {
  let testUserId: string;
  let testTutorId: string;

  beforeEach(async () => {
    // Create a test user and tutor profile
    const testUser = await prisma.user.create({
      data: {
        email: `career-test-${Date.now()}@example.com`,
        name: 'Test Tutor User',
        password: 'hashedpassword',
        role: 'user'
      }
    });
    testUserId = testUser.id;

    const tutorProfile = await prisma.tutorProfile.create({
      data: {
        userId: testUserId,
        title: 'Test Tutor',
        bio: 'Test bio',
        rate: 50,
        status: 'approved'
      }
    });
    testTutorId = tutorProfile.id;
  });

  afterEach(async () => {
    // Clean up test data
    await prisma.tutorCareer.deleteMany({
      where: { tutorId: testTutorId }
    }).catch(() => {});

    await prisma.tutorProfile.delete({
      where: { id: testTutorId }
    }).catch(() => {});

    await prisma.user.delete({
      where: { id: testUserId }
    }).catch(() => {});
  });

  describe('addCareer', () => {
    it('should add current career record successfully', async () => {
      const careerData: CreateCareerData = {
        tutorId: testTutorId,
        title: 'Senior Software Engineer',
        company: 'Tech Corp',
        startYear: 2020,
        current: true,
        description: 'Leading development team'
      };

      const career = await TutorCareerService.addCareer(careerData);

      expect(career).toBeDefined();
      expect(career.tutorId).toBe(testTutorId);
      expect(career.title).toBe('Senior Software Engineer');
      expect(career.company).toBe('Tech Corp');
      expect(career.startYear).toBe(2020);
      expect(career.current).toBe(true);
      expect(career.endYear).toBeNull();
      expect(career.description).toBe('Leading development team');
    });

    it('should add past career record successfully', async () => {
      const careerData: CreateCareerData = {
        tutorId: testTutorId,
        title: 'Software Engineer',
        company: 'Old Corp',
        startYear: 2018,
        endYear: 2020,
        current: false,
        description: 'Full-stack development'
      };

      const career = await TutorCareerService.addCareer(careerData);

      expect(career).toBeDefined();
      expect(career.current).toBe(false);
      expect(career.endYear).toBe(2020);
    });

    it('should throw error for invalid start year', async () => {
      const careerData: CreateCareerData = {
        tutorId: testTutorId,
        title: 'Engineer',
        company: 'Company',
        startYear: 1800, // Too early
        current: true
      };

      await expect(TutorCareerService.addCareer(careerData))
        .rejects.toThrow('Invalid start year');
    });

    it('should throw error for current position with end year', async () => {
      const careerData: CreateCareerData = {
        tutorId: testTutorId,
        title: 'Engineer',
        company: 'Company',
        startYear: 2020,
        endYear: 2023, // Should not have end year if current
        current: true
      };

      await expect(TutorCareerService.addCareer(careerData))
        .rejects.toThrow('Current position cannot have an end year');
    });

    it('should throw error for non-current position without end year', async () => {
      const careerData: CreateCareerData = {
        tutorId: testTutorId,
        title: 'Engineer',
        company: 'Company',
        startYear: 2020,
        current: false
        // Missing endYear
      };

      await expect(TutorCareerService.addCareer(careerData))
        .rejects.toThrow('Non-current position must have an end year');
    });

    it('should update other current positions when adding new current position', async () => {
      // Add first current position
      const firstCareer = await TutorCareerService.addCareer({
        tutorId: testTutorId,
        title: 'Engineer',
        company: 'Company A',
        startYear: 2018,
        current: true
      });

      // Add second current position
      await TutorCareerService.addCareer({
        tutorId: testTutorId,
        title: 'Senior Engineer',
        company: 'Company B',
        startYear: 2020,
        current: true
      });

      // Check that first position is no longer current
      const updatedFirstCareer = await TutorCareerService.getCareerById(firstCareer.id);
      expect(updatedFirstCareer!.current).toBe(false);
    });

    it('should throw error if tutor does not exist', async () => {
      const careerData: CreateCareerData = {
        tutorId: 'non-existent-tutor-id',
        title: 'Engineer',
        company: 'Company',
        startYear: 2020,
        current: true
      };

      await expect(TutorCareerService.addCareer(careerData))
        .rejects.toThrow('Tutor profile not found');
    });
  });

  describe('getTutorCareer', () => {
    beforeEach(async () => {
      // Add some test career records
      await TutorCareerService.addCareer({
        tutorId: testTutorId,
        title: 'Junior Engineer',
        company: 'Company A',
        startYear: 2018,
        endYear: 2020,
        current: false
      });

      await TutorCareerService.addCareer({
        tutorId: testTutorId,
        title: 'Senior Engineer',
        company: 'Company B',
        startYear: 2020,
        current: true
      });
    });

    it('should return all career records for a tutor', async () => {
      const careers = await TutorCareerService.getTutorCareer(testTutorId);

      expect(careers).toHaveLength(2);
      // Should be ordered by current first, then by start year descending
      expect(careers[0].current).toBe(true); // Current position first
      expect(careers[0].title).toBe('Senior Engineer');
      expect(careers[1].current).toBe(false);
      expect(careers[1].title).toBe('Junior Engineer');
    });

    it('should return empty array for tutor with no career', async () => {
      // Create another tutor
      const anotherUser = await prisma.user.create({
        data: {
          email: `no-career-${Date.now()}@example.com`,
          name: 'No Career User',
          password: 'hashedpassword'
        }
      });

      const anotherTutor = await prisma.tutorProfile.create({
        data: {
          userId: anotherUser.id,
          title: 'Another Tutor',
          status: 'approved'
        }
      });

      const careers = await TutorCareerService.getTutorCareer(anotherTutor.id);
      expect(careers).toHaveLength(0);

      // Clean up
      await prisma.tutorProfile.delete({ where: { id: anotherTutor.id } });
      await prisma.user.delete({ where: { id: anotherUser.id } });
    });
  });

  describe('updateCareer', () => {
    let careerId: string;

    beforeEach(async () => {
      const career = await TutorCareerService.addCareer({
        tutorId: testTutorId,
        title: 'Engineer',
        company: 'Company',
        startYear: 2020,
        endYear: 2022,
        current: false
      });
      careerId = career.id;
    });

    it('should update career record successfully', async () => {
      const updateData = {
        title: 'Senior Engineer',
        company: 'New Company',
        description: 'Updated description'
      };

      const updatedCareer = await TutorCareerService.updateCareer(careerId, updateData);

      expect(updatedCareer.title).toBe('Senior Engineer');
      expect(updatedCareer.company).toBe('New Company');
      expect(updatedCareer.description).toBe('Updated description');
      expect(updatedCareer.startYear).toBe(2020); // Should remain unchanged
    });

    it('should update career to current position', async () => {
      const updateData = {
        current: true
      };

      const updatedCareer = await TutorCareerService.updateCareer(careerId, updateData);

      expect(updatedCareer.current).toBe(true);
      expect(updatedCareer.endYear).toBeNull(); // Should be null for current position
    });

    it('should throw error if career record does not exist', async () => {
      const updateData = {
        title: 'Updated Title'
      };

      await expect(TutorCareerService.updateCareer('non-existent-id', updateData))
        .rejects.toThrow('Career record not found');
    });
  });

  describe('deleteCareer', () => {
    let careerId: string;

    beforeEach(async () => {
      const career = await TutorCareerService.addCareer({
        tutorId: testTutorId,
        title: 'Engineer',
        company: 'Company',
        startYear: 2020,
        current: true
      });
      careerId = career.id;
    });

    it('should delete career record successfully', async () => {
      await TutorCareerService.deleteCareer(careerId);

      const careers = await TutorCareerService.getTutorCareer(testTutorId);
      expect(careers).toHaveLength(0);
    });

    it('should throw error if career record does not exist', async () => {
      await expect(TutorCareerService.deleteCareer('non-existent-id'))
        .rejects.toThrow('Career record not found');
    });
  });

  describe('getCurrentPosition', () => {
    beforeEach(async () => {
      await TutorCareerService.addCareer({
        tutorId: testTutorId,
        title: 'Past Engineer',
        company: 'Old Company',
        startYear: 2018,
        endYear: 2020,
        current: false
      });

      await TutorCareerService.addCareer({
        tutorId: testTutorId,
        title: 'Current Engineer',
        company: 'Current Company',
        startYear: 2020,
        current: true
      });
    });

    it('should return current position', async () => {
      const currentPosition = await TutorCareerService.getCurrentPosition(testTutorId);

      expect(currentPosition).toBeDefined();
      expect(currentPosition!.title).toBe('Current Engineer');
      expect(currentPosition!.current).toBe(true);
    });

    it('should return null if no current position', async () => {
      // Create another tutor with no current position
      const anotherUser = await prisma.user.create({
        data: {
          email: `no-current-${Date.now()}@example.com`,
          name: 'No Current User',
          password: 'hashedpassword'
        }
      });

      const anotherTutor = await prisma.tutorProfile.create({
        data: {
          userId: anotherUser.id,
          title: 'Another Tutor',
          status: 'approved'
        }
      });

      const currentPosition = await TutorCareerService.getCurrentPosition(anotherTutor.id);
      expect(currentPosition).toBeNull();

      // Clean up
      await prisma.tutorProfile.delete({ where: { id: anotherTutor.id } });
      await prisma.user.delete({ where: { id: anotherUser.id } });
    });
  });

  describe('getTotalExperience', () => {
    beforeEach(async () => {
      await TutorCareerService.addCareer({
        tutorId: testTutorId,
        title: 'Junior Engineer',
        company: 'Company A',
        startYear: 2018,
        endYear: 2020,
        current: false
      });

      await TutorCareerService.addCareer({
        tutorId: testTutorId,
        title: 'Senior Engineer',
        company: 'Company B',
        startYear: 2020,
        current: true
      });
    });

    it('should calculate total years of experience', async () => {
      const totalExperience = await TutorCareerService.getTotalExperience(testTutorId);
      const currentYear = new Date().getFullYear();

      // 2 years (2018-2020) + (2020-current year) years
      const expectedExperience = 2 + (currentYear - 2020);
      expect(totalExperience).toBe(expectedExperience);
    });
  });

  describe('getCareerSummary', () => {
    beforeEach(async () => {
      await TutorCareerService.addCareer({
        tutorId: testTutorId,
        title: 'Junior Engineer',
        company: 'Company A',
        startYear: 2018,
        endYear: 2020,
        current: false
      });

      await TutorCareerService.addCareer({
        tutorId: testTutorId,
        title: 'Senior Engineer',
        company: 'Company B',
        startYear: 2020,
        current: true
      });
    });

    it('should return career summary', async () => {
      const summary = await TutorCareerService.getCareerSummary(testTutorId);

      expect(summary).toBeDefined();
      expect(summary.totalPositions).toBe(2);
      expect(summary.companies).toEqual(expect.arrayContaining(['Company A', 'Company B']));
      expect(summary.companies).toHaveLength(2);
      expect(summary.currentPosition).toBeDefined();
      expect(summary.currentPosition!.title).toBe('Senior Engineer');
      expect(summary.totalExperience).toBeGreaterThan(0);
    });
  });
});
