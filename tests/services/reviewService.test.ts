import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import { prisma } from '../../src/lib/prisma';
import { ReviewService, CreateReviewData } from '../../src/services/reviewService';

describe('ReviewService', () => {
  let testStudentId: string;
  let testTutorUserId: string;
  let testTutorId: string;
  let testAppointmentId: string;

  beforeEach(async () => {
    // Create a test student
    const testStudent = await prisma.user.create({
      data: {
        email: `review-student-${Date.now()}@example.com`,
        name: 'Test Student',
        password: 'hashedpassword',
        role: 'user'
      }
    });
    testStudentId = testStudent.id;

    // Create a test tutor user
    const testTutorUser = await prisma.user.create({
      data: {
        email: `review-tutor-${Date.now()}@example.com`,
        name: 'Test Tutor',
        password: 'hashedpassword',
        role: 'user'
      }
    });
    testTutorUserId = testTutorUser.id;

    // Create tutor profile
    const tutorProfile = await prisma.tutorProfile.create({
      data: {
        userId: testTutorUserId,
        title: 'Test Tutor',
        bio: 'Test bio',
        rate: 100,
        status: 'approved'
      }
    });
    testTutorId = tutorProfile.id;

    // Create a completed appointment
    const appointment = await prisma.appointment.create({
      data: {
        tutorId: testTutorId,
        studentId: testStudentId,
        startTime: new Date('2023-01-01T10:00:00Z'),
        endTime: new Date('2023-01-01T11:00:00Z'),
        meetingType: 'online',
        meetingLink: 'https://zoom.us/j/123456789',
        status: 'completed'
      }
    });
    testAppointmentId = appointment.id;
  });

  afterEach(async () => {
    // Clean up test data in correct order
    try {
      await prisma.review.deleteMany({
        where: {
          OR: [
            { tutorId: testTutorId },
            { appointmentId: testAppointmentId }
          ]
        }
      });

      await prisma.appointment.deleteMany({
        where: {
          OR: [
            { tutorId: testTutorId },
            { studentId: testStudentId }
          ]
        }
      });

      await prisma.tutorProfile.delete({
        where: { id: testTutorId }
      });

      await prisma.user.deleteMany({
        where: {
          id: { in: [testStudentId, testTutorUserId] }
        }
      });
    } catch (error) {
      // Ignore cleanup errors in tests
    }
  });

  describe('createReview', () => {
    it('should create review successfully', async () => {
      const reviewData: CreateReviewData = {
        appointmentId: testAppointmentId,
        tutorId: testTutorId,
        rating: 5,
        comment: 'Excellent tutor!'
      };

      const review = await ReviewService.createReview(reviewData, testStudentId);

      expect(review).toBeDefined();
      expect(review.appointmentId).toBe(testAppointmentId);
      expect(review.tutorId).toBe(testTutorId);
      expect(review.rating).toBe(5);
      expect(review.comment).toBe('Excellent tutor!');
    });

    it('should throw error for invalid rating', async () => {
      const reviewData: CreateReviewData = {
        appointmentId: testAppointmentId,
        tutorId: testTutorId,
        rating: 6, // Invalid rating
        comment: 'Test comment'
      };

      await expect(ReviewService.createReview(reviewData, testStudentId))
        .rejects.toThrow('Rating must be between 1 and 5');
    });

    it('should throw error if appointment not found', async () => {
      const reviewData: CreateReviewData = {
        appointmentId: 'non-existent-appointment',
        tutorId: testTutorId,
        rating: 5,
        comment: 'Test comment'
      };

      await expect(ReviewService.createReview(reviewData, testStudentId))
        .rejects.toThrow('Appointment not found');
    });

    it('should throw error if appointment is not completed', async () => {
      // Create a scheduled appointment
      const scheduledAppointment = await prisma.appointment.create({
        data: {
          tutorId: testTutorId,
          studentId: testStudentId,
          startTime: new Date('2024-01-01T10:00:00Z'),
          endTime: new Date('2024-01-01T11:00:00Z'),
          meetingType: 'online',
          meetingLink: 'https://zoom.us/j/123456789',
          status: 'scheduled'
        }
      });

      const reviewData: CreateReviewData = {
        appointmentId: scheduledAppointment.id,
        tutorId: testTutorId,
        rating: 5,
        comment: 'Test comment'
      };

      await expect(ReviewService.createReview(reviewData, testStudentId))
        .rejects.toThrow('Can only review completed appointments');

      // Clean up
      await prisma.appointment.delete({ where: { id: scheduledAppointment.id } });
    });

    it('should throw error if user is not the student of the appointment', async () => {
      // Create another student
      const anotherStudent = await prisma.user.create({
        data: {
          email: `another-student-${Date.now()}@example.com`,
          name: 'Another Student',
          password: 'hashedpassword'
        }
      });

      const reviewData: CreateReviewData = {
        appointmentId: testAppointmentId,
        tutorId: testTutorId,
        rating: 5,
        comment: 'Test comment'
      };

      await expect(ReviewService.createReview(reviewData, anotherStudent.id))
        .rejects.toThrow('You can only review your own appointments');

      // Clean up
      await prisma.user.delete({ where: { id: anotherStudent.id } });
    });

    it('should throw error if tutor ID does not match appointment', async () => {
      // Create another tutor
      const anotherTutorUser = await prisma.user.create({
        data: {
          email: `another-tutor-${Date.now()}@example.com`,
          name: 'Another Tutor',
          password: 'hashedpassword'
        }
      });

      const anotherTutor = await prisma.tutorProfile.create({
        data: {
          userId: anotherTutorUser.id,
          title: 'Another Tutor',
          status: 'approved'
        }
      });

      const reviewData: CreateReviewData = {
        appointmentId: testAppointmentId,
        tutorId: anotherTutor.id, // Wrong tutor ID
        rating: 5,
        comment: 'Test comment'
      };

      await expect(ReviewService.createReview(reviewData, testStudentId))
        .rejects.toThrow('Tutor ID does not match appointment');

      // Clean up
      await prisma.tutorProfile.delete({ where: { id: anotherTutor.id } });
      await prisma.user.delete({ where: { id: anotherTutorUser.id } });
    });

    it('should throw error if review already exists', async () => {
      // Create first review
      const reviewData: CreateReviewData = {
        appointmentId: testAppointmentId,
        tutorId: testTutorId,
        rating: 5,
        comment: 'First review'
      };

      await ReviewService.createReview(reviewData, testStudentId);

      // Try to create second review for same appointment
      const secondReviewData: CreateReviewData = {
        appointmentId: testAppointmentId,
        tutorId: testTutorId,
        rating: 4,
        comment: 'Second review'
      };

      await expect(ReviewService.createReview(secondReviewData, testStudentId))
        .rejects.toThrow('Review already exists for this appointment');
    });
  });

  describe('getReviewById', () => {
    let reviewId: string;

    beforeEach(async () => {
      const review = await ReviewService.createReview({
        appointmentId: testAppointmentId,
        tutorId: testTutorId,
        rating: 5,
        comment: 'Test review'
      }, testStudentId);
      reviewId = review.id;
    });

    it('should return review with details', async () => {
      const review = await ReviewService.getReviewById(reviewId);

      expect(review).toBeDefined();
      expect(review!.id).toBe(reviewId);
      expect(review!.rating).toBe(5);
      expect(review!.comment).toBe('Test review');
      expect(review!.appointment).toBeDefined();
      expect(review!.tutor).toBeDefined();
      expect(review!.appointment.student.name).toBe('Test Student');
      expect(review!.tutor.user.name).toBe('Test Tutor');
    });

    it('should return null for non-existent review', async () => {
      const review = await ReviewService.getReviewById('non-existent-id');
      expect(review).toBeNull();
    });
  });

  describe('getTutorReviews', () => {
    beforeEach(async () => {
      // Create multiple reviews
      await ReviewService.createReview({
        appointmentId: testAppointmentId,
        tutorId: testTutorId,
        rating: 5,
        comment: 'Excellent!'
      }, testStudentId);

      // Create another appointment and review
      const anotherAppointment = await prisma.appointment.create({
        data: {
          tutorId: testTutorId,
          studentId: testStudentId,
          startTime: new Date('2023-01-02T10:00:00Z'),
          endTime: new Date('2023-01-02T11:00:00Z'),
          meetingType: 'online',
          meetingLink: 'https://zoom.us/j/123456789',
          status: 'completed'
        }
      });

      await ReviewService.createReview({
        appointmentId: anotherAppointment.id,
        tutorId: testTutorId,
        rating: 4,
        comment: 'Good!'
      }, testStudentId);
    });

    it('should return tutor reviews with pagination', async () => {
      const result = await ReviewService.getTutorReviews(testTutorId, {
        page: 1,
        limit: 10
      });

      expect(result.reviews).toHaveLength(2);
      expect(result.total).toBe(2);
      expect(result.page).toBe(1);
      expect(result.limit).toBe(10);
      expect(result.totalPages).toBe(1);
    });

    it('should filter reviews by rating', async () => {
      const result = await ReviewService.getTutorReviews(testTutorId, {
        rating: 5
      });

      expect(result.reviews).toHaveLength(1);
      expect(result.reviews[0].rating).toBe(5);
    });
  });

  describe('updateReview', () => {
    let reviewId: string;

    beforeEach(async () => {
      const review = await ReviewService.createReview({
        appointmentId: testAppointmentId,
        tutorId: testTutorId,
        rating: 4,
        comment: 'Original comment'
      }, testStudentId);
      reviewId = review.id;
    });

    it('should update review successfully', async () => {
      const updateData = {
        rating: 5,
        comment: 'Updated comment'
      };

      const updatedReview = await ReviewService.updateReview(reviewId, updateData, testStudentId);

      expect(updatedReview.rating).toBe(5);
      expect(updatedReview.comment).toBe('Updated comment');
    });

    it('should throw error for invalid rating', async () => {
      const updateData = {
        rating: 6
      };

      await expect(ReviewService.updateReview(reviewId, updateData, testStudentId))
        .rejects.toThrow('Rating must be between 1 and 5');
    });

    it('should throw error if review not found', async () => {
      const updateData = {
        rating: 5
      };

      await expect(ReviewService.updateReview('non-existent-id', updateData, testStudentId))
        .rejects.toThrow('Review not found');
    });

    it('should throw error if user is not the owner', async () => {
      // Create another student
      const anotherStudent = await prisma.user.create({
        data: {
          email: `another-student-update-${Date.now()}@example.com`,
          name: 'Another Student',
          password: 'hashedpassword'
        }
      });

      const updateData = {
        rating: 5
      };

      await expect(ReviewService.updateReview(reviewId, updateData, anotherStudent.id))
        .rejects.toThrow('You can only update your own reviews');

      // Clean up
      await prisma.user.delete({ where: { id: anotherStudent.id } });
    });
  });

  describe('getTutorReviewStats', () => {
    beforeEach(async () => {
      // Create multiple reviews with different ratings
      await ReviewService.createReview({
        appointmentId: testAppointmentId,
        tutorId: testTutorId,
        rating: 5,
        comment: 'Excellent!'
      }, testStudentId);

      // Create more appointments and reviews
      for (let i = 0; i < 4; i++) {
        const appointment = await prisma.appointment.create({
          data: {
            tutorId: testTutorId,
            studentId: testStudentId,
            startTime: new Date(`2023-01-0${i + 2}T10:00:00Z`),
            endTime: new Date(`2023-01-0${i + 2}T11:00:00Z`),
            meetingType: 'online',
            meetingLink: 'https://zoom.us/j/123456789',
            status: 'completed'
          }
        });

        await ReviewService.createReview({
          appointmentId: appointment.id,
          tutorId: testTutorId,
          rating: i + 2, // Ratings: 2, 3, 4, 5
          comment: `Review ${i + 1}`
        }, testStudentId);
      }
    });

    it('should return tutor review statistics', async () => {
      const stats = await ReviewService.getTutorReviewStats(testTutorId);

      expect(stats.totalReviews).toBe(5);
      expect(stats.averageRating).toBe(3.8); // (5+2+3+4+5)/5 = 3.8
      expect(stats.ratingDistribution[5]).toBe(2);
      expect(stats.ratingDistribution[4]).toBe(1);
      expect(stats.ratingDistribution[3]).toBe(1);
      expect(stats.ratingDistribution[2]).toBe(1);
      expect(stats.ratingDistribution[1]).toBe(0);
      expect(stats.recentReviews).toHaveLength(5);
    });
  });
});
