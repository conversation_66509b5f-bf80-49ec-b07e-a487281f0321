import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import { TestUtils } from '../helpers/testUtils';
import { TutorService } from '../../src/services/tutorService';
import { AppointmentService } from '../../src/services/appointmentService';
import { ReviewService } from '../../src/services/reviewService';

describe('Simple Integration Tests', () => {
  beforeEach(async () => {
    await TestUtils.cleanDatabase();
  });

  afterEach(async () => {
    await TestUtils.cleanDatabase();
  });

  describe('TutorService', () => {
    it('should create and retrieve tutor', async () => {
      const user = await TestUtils.createTestUser();

      const tutorData = {
        userId: user.id,
        title: 'Math Tutor',
        bio: 'Experienced math teacher',
        rate: 50,
        education: [{
          institution: 'MIT',
          degree: 'PhD',
          fieldOfStudy: 'Mathematics',
          startYear: 2010,
          endYear: 2014
        }],
        career: [{
          title: 'Professor',
          company: 'University',
          startYear: 2015,
          current: true
        }]
      };

      const tutor = await TutorService.applyToBecomeTutor(tutorData);
      expect(tutor).toBeDefined();
      expect(tutor.title).toBe('Math Tutor');
      expect(tutor.status).toBe('pending');

      const retrieved = await TutorService.getTutorById(tutor.id);
      expect(retrieved).toBeDefined();
      expect(retrieved!.title).toBe('Math Tutor');
    });
  });

  describe('AppointmentService', () => {
    it('should create appointment', async () => {
      const { studentId, tutorId } = await TestUtils.createTestStudentAndTutor();

      const nextMonday = TestUtils.getNextMonday();
      const startTime = new Date(nextMonday);
      startTime.setHours(10, 0, 0, 0);
      const endTime = new Date(nextMonday);
      endTime.setHours(11, 0, 0, 0);

      const appointmentData = {
        tutorId,
        studentId,
        startTime,
        endTime,
        meetingType: 'online' as const,
        meetingLink: 'https://zoom.us/j/*********'
      };

      const appointment = await AppointmentService.createAppointment(appointmentData);
      expect(appointment).toBeDefined();
      expect(appointment.tutorId).toBe(tutorId);
      expect(appointment.studentId).toBe(studentId);
      expect(appointment.status).toBe('scheduled');
    });
  });

  describe('ReviewService', () => {
    it('should create review', async () => {
      const { appointmentId, tutorId } = await TestUtils.createTestAppointmentWithDependencies({
        status: 'completed'
      });

      const reviewData = {
        appointmentId,
        tutorId,
        rating: 5,
        comment: 'Excellent tutor!'
      };

      // Get the student ID from the appointment
      const appointment = await AppointmentService.getAppointmentById(appointmentId);
      const studentId = appointment!.studentId;

      const review = await ReviewService.createReview(reviewData, studentId);
      expect(review).toBeDefined();
      expect(review.rating).toBe(5);
      expect(review.comment).toBe('Excellent tutor!');
    });
  });

  describe('Integration Flow', () => {
    it('should handle complete tutoring flow', async () => {
      // 1. Create users
      const student = await TestUtils.createTestUser(
        TestUtils.generateUniqueEmail('student'),
        'Test Student'
      );

      const tutorUser = await TestUtils.createTestUser(
        TestUtils.generateUniqueEmail('tutor'),
        'Test Tutor'
      );

      // 2. Apply to become tutor
      const tutorData = {
        userId: tutorUser.id,
        title: 'Math Tutor',
        bio: 'Experienced math teacher',
        rate: 50,
        education: [{
          institution: 'MIT',
          degree: 'PhD',
          fieldOfStudy: 'Mathematics',
          startYear: 2010,
          endYear: 2014
        }],
        career: [{
          title: 'Professor',
          company: 'University',
          startYear: 2015,
          current: true
        }]
      };

      const tutor = await TutorService.applyToBecomeTutor(tutorData);

      // 3. Approve tutor
      await TutorService.updateTutorStatus(tutor.id, 'approved');

      // 4. Add availability
      await TestUtils.createTestAvailability(tutor.id, 1, '09:00', '17:00');

      // 5. Create appointment
      const nextMonday = TestUtils.getNextMonday();
      const startTime = new Date(nextMonday);
      startTime.setHours(10, 0, 0, 0);
      const endTime = new Date(nextMonday);
      endTime.setHours(11, 0, 0, 0);

      const appointment = await AppointmentService.createAppointment({
        tutorId: tutor.id,
        studentId: student.id,
        startTime,
        endTime,
        meetingType: 'online',
        meetingLink: 'https://zoom.us/j/*********'
      });

      // 6. Complete appointment
      await AppointmentService.completeAppointment(appointment.id);

      // 7. Create review
      const review = await ReviewService.createReview({
        appointmentId: appointment.id,
        tutorId: tutor.id,
        rating: 5,
        comment: 'Excellent session!'
      }, student.id);

      // 8. Verify everything
      const finalTutor = await TutorService.getTutorById(tutor.id);
      expect(finalTutor).toBeDefined();
      expect(finalTutor!.status).toBe('approved');

      const finalAppointment = await AppointmentService.getAppointmentById(appointment.id);
      expect(finalAppointment).toBeDefined();
      expect(finalAppointment!.status).toBe('completed');

      const finalReview = await ReviewService.getReviewById(review.id);
      expect(finalReview).toBeDefined();
      expect(finalReview!.rating).toBe(5);

      // 9. Check tutor stats
      const stats = await ReviewService.getTutorReviewStats(tutor.id);
      expect(stats.totalReviews).toBe(1);
      expect(stats.averageRating).toBe(5);
    });
  });
});
