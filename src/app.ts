import fastify, { FastifyInstance, FastifyError, FastifyReply, FastifyRequest } from 'fastify';
import cors from '@fastify/cors';
import helmet from '@fastify/helmet';
// import { Headers, Request } from 'node-fetch';
import envPlugin from './config/env';
import { registerSwagger } from './config/swagger';
import { auth } from './lib/auth';
import { prisma } from './lib/prisma';
import { userRoutes } from './routes/users';
import { authRoutes } from './routes/auth';
import { InitService } from './services/initService';

export async function createApp(): Promise<FastifyInstance> {
  const app = fastify({
    logger: {
      level: 'info',
      transport: {
        target: 'pino-pretty',
        options: {
          translateTime: 'HH:MM:ss Z',
          ignore: 'pid,hostname',
        },
      },
    },
    disableRequestLogging: process.env.NODE_ENV === 'production',
    trustProxy: true, // Trust proxy headers for secure cookies
  });

  // Register environment variables first
  await app.register(envPlugin);

  // Add prisma to app instance
  app.decorate('prisma', prisma);


  // Configure security headers first
  await app.register(helmet, {
    contentSecurityPolicy: false, // Disable CSP for now to avoid conflicts
    crossOriginEmbedderPolicy: false, // Required for some auth flows
    crossOriginOpenerPolicy: { policy: 'same-origin' },
    crossOriginResourcePolicy: { policy: 'same-site' },
  });

  // Configure CORS with settings from environment
  const corsOptions = {
    origin: app.config.NODE_ENV === 'production'
      ? app.config.CORS_ORIGIN?.split(',').map(origin => origin.trim())
      : '*',
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
    exposedHeaders: ['Content-Range', 'X-Total-Count'],
    credentials: true,
    maxAge: 600, // 10 minutes
  };

  // Register CORS with the provided options
  await app.register(cors, corsOptions);

  // Register Swagger before defining routes
  await registerSwagger(app);

  // Initialize default accounts
  await InitService.initializeDefaultAccounts(app);

  // Register user routes
  await app.register(userRoutes, { prefix: '/api/users' });

  // Register auth routes
  await app.register(authRoutes, { prefix: '/api/auth' });

  // Health check route with OpenAPI documentation
  app.get(
    '/health',
    {
      schema: {
        tags: ['Health'],
        summary: 'Health Check',
        description: 'Check the health status of the API server',
        operationId: 'healthCheck',
        security: [], // No auth required for health check
        response: {
          200: {
            description: 'Successful health check response',
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  properties: {
                    status: {
                      type: 'string',
                      enum: ['ok'],
                      description: 'Service status',
                      example: 'ok'
                    },
                    timestamp: {
                      type: 'string',
                      format: 'date-time',
                      description: 'Current server time',
                      example: '2025-06-29T13:15:05.000Z'
                    },
                    environment: {
                      type: 'string',
                      description: 'Current environment',
                      example: 'development'
                    },
                  },
                  required: ['status', 'timestamp', 'environment']
                }
              }
            }
          },
          500: {
            description: 'Server Error',
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  properties: {
                    statusCode: { type: 'number' },
                    error: { type: 'string' },
                    message: { type: 'string' }
                  }
                }
              }
            }
          }
        },
        openapi: {
          tags: [{ name: 'Health' }]
        }
      } as const,
    },
    async () => {
      return {
        status: 'ok' as const,
        timestamp: new Date().toISOString(),
        environment: app.config.NODE_ENV,
      };
    },
  );

  // Example protected route
  app.get('/protected', async (_request, _reply) => {
    // Note: The session logic will be added here in the next step.
    return { message: 'This is a protected route, but still needs session validation.' };
  });

  // Error handling
  app.setErrorHandler((error: FastifyError, request: FastifyRequest, reply: FastifyReply) => {
    const statusCode = error.statusCode || 500;

    // Log the error
    request.log.error({
      req: request,
      res: reply,
      err: error,
      msg: error.message
    }, 'Request error');

    // Send error response
    reply.status(statusCode).send({
      statusCode,
      error: statusCode === 500 ? 'Internal Server Error' : error.name,
      message: statusCode === 500 ? 'An internal server error occurred' : error.message
    });
  });

  // Add type for rawBody
  app.addHook('preValidation', (request: FastifyRequest, _reply: FastifyReply, done: (err?: Error) => void) => {
    (request as any).rawBody = request.body;
    done();
  });



  // Handle 404 - Keep this as the last route
  app.setNotFoundHandler((request, reply) => {
    reply.status(404).send({
      statusCode: 404,
      error: 'Not Found',
      message: `Route ${request.method}:${request.url} not found`
    });
  });

  return app;
}
