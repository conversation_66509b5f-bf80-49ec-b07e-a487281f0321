import { prisma } from '../lib/prisma';
import { TutorAvailability } from '@prisma/client';

export interface CreateAvailabilityData {
  tutorId: string;
  dayOfWeek: number; // 0-6 (Sunday-Saturday)
  startTime: string; // Format: "HH:MM"
  endTime: string; // Format: "HH:MM"
}

export interface UpdateAvailabilityData {
  dayOfWeek?: number;
  startTime?: string;
  endTime?: string;
}

export class TutorAvailabilityService {
  /**
   * Add availability slot for a tutor
   */
  static async addAvailability(data: CreateAvailabilityData): Promise<TutorAvailability> {
    // Validate time format
    if (!this.isValidTimeFormat(data.startTime) || !this.isValidTimeFormat(data.endTime)) {
      throw new Error('Invalid time format. Use HH:MM format');
    }

    // Validate day of week
    if (data.dayOfWeek < 0 || data.dayOfWeek > 6) {
      throw new Error('Invalid day of week. Use 0-6 (Sunday-Saturday)');
    }

    // Validate start time is before end time
    if (data.startTime >= data.endTime) {
      throw new Error('Start time must be before end time');
    }

    // Check if tutor exists
    const tutor = await prisma.tutorProfile.findUnique({
      where: { id: data.tutorId }
    });

    if (!tutor) {
      throw new Error('Tutor profile not found');
    }

    // Check for overlapping availability on the same day
    const overlapping = await prisma.tutorAvailability.findFirst({
      where: {
        tutorId: data.tutorId,
        dayOfWeek: data.dayOfWeek,
        OR: [
          {
            AND: [
              { startTime: { lte: data.startTime } },
              { endTime: { gt: data.startTime } }
            ]
          },
          {
            AND: [
              { startTime: { lt: data.endTime } },
              { endTime: { gte: data.endTime } }
            ]
          },
          {
            AND: [
              { startTime: { gte: data.startTime } },
              { endTime: { lte: data.endTime } }
            ]
          }
        ]
      }
    });

    if (overlapping) {
      throw new Error('Time slot overlaps with existing availability');
    }

    return await prisma.tutorAvailability.create({
      data
    });
  }

  /**
   * Get all availability slots for a tutor
   */
  static async getTutorAvailability(tutorId: string): Promise<TutorAvailability[]> {
    return await prisma.tutorAvailability.findMany({
      where: { tutorId },
      orderBy: [
        { dayOfWeek: 'asc' },
        { startTime: 'asc' }
      ]
    });
  }

  /**
   * Update availability slot
   */
  static async updateAvailability(
    availabilityId: string,
    data: UpdateAvailabilityData
  ): Promise<TutorAvailability> {
    const existing = await prisma.tutorAvailability.findUnique({
      where: { id: availabilityId }
    });

    if (!existing) {
      throw new Error('Availability slot not found');
    }

    // Validate time format if provided
    if (data.startTime && !this.isValidTimeFormat(data.startTime)) {
      throw new Error('Invalid start time format. Use HH:MM format');
    }

    if (data.endTime && !this.isValidTimeFormat(data.endTime)) {
      throw new Error('Invalid end time format. Use HH:MM format');
    }

    // Validate day of week if provided
    if (data.dayOfWeek !== undefined && (data.dayOfWeek < 0 || data.dayOfWeek > 6)) {
      throw new Error('Invalid day of week. Use 0-6 (Sunday-Saturday)');
    }

    // Prepare updated data
    const updatedData = {
      dayOfWeek: data.dayOfWeek ?? existing.dayOfWeek,
      startTime: data.startTime ?? existing.startTime,
      endTime: data.endTime ?? existing.endTime
    };

    // Validate start time is before end time
    if (updatedData.startTime >= updatedData.endTime) {
      throw new Error('Start time must be before end time');
    }

    // Check for overlapping availability on the same day (excluding current slot)
    const overlapping = await prisma.tutorAvailability.findFirst({
      where: {
        tutorId: existing.tutorId,
        dayOfWeek: updatedData.dayOfWeek,
        id: { not: availabilityId },
        OR: [
          {
            AND: [
              { startTime: { lte: updatedData.startTime } },
              { endTime: { gt: updatedData.startTime } }
            ]
          },
          {
            AND: [
              { startTime: { lt: updatedData.endTime } },
              { endTime: { gte: updatedData.endTime } }
            ]
          },
          {
            AND: [
              { startTime: { gte: updatedData.startTime } },
              { endTime: { lte: updatedData.endTime } }
            ]
          }
        ]
      }
    });

    if (overlapping) {
      throw new Error('Time slot overlaps with existing availability');
    }

    return await prisma.tutorAvailability.update({
      where: { id: availabilityId },
      data: updatedData
    });
  }

  /**
   * Delete availability slot
   */
  static async deleteAvailability(availabilityId: string): Promise<void> {
    const existing = await prisma.tutorAvailability.findUnique({
      where: { id: availabilityId }
    });

    if (!existing) {
      throw new Error('Availability slot not found');
    }

    await prisma.tutorAvailability.delete({
      where: { id: availabilityId }
    });
  }

  /**
   * Get availability for a specific day
   */
  static async getAvailabilityByDay(tutorId: string, dayOfWeek: number): Promise<TutorAvailability[]> {
    if (dayOfWeek < 0 || dayOfWeek > 6) {
      throw new Error('Invalid day of week. Use 0-6 (Sunday-Saturday)');
    }

    return await prisma.tutorAvailability.findMany({
      where: {
        tutorId,
        dayOfWeek
      },
      orderBy: { startTime: 'asc' }
    });
  }

  /**
   * Bulk update availability for a tutor
   */
  static async bulkUpdateAvailability(
    tutorId: string,
    availabilities: CreateAvailabilityData[]
  ): Promise<TutorAvailability[]> {
    // Validate tutor exists
    const tutor = await prisma.tutorProfile.findUnique({
      where: { id: tutorId }
    });

    if (!tutor) {
      throw new Error('Tutor profile not found');
    }

    // Validate all availability data
    for (const availability of availabilities) {
      if (!this.isValidTimeFormat(availability.startTime) || !this.isValidTimeFormat(availability.endTime)) {
        throw new Error('Invalid time format. Use HH:MM format');
      }

      if (availability.dayOfWeek < 0 || availability.dayOfWeek > 6) {
        throw new Error('Invalid day of week. Use 0-6 (Sunday-Saturday)');
      }

      if (availability.startTime >= availability.endTime) {
        throw new Error('Start time must be before end time');
      }
    }

    // Check for overlaps within the new data
    for (let i = 0; i < availabilities.length; i++) {
      for (let j = i + 1; j < availabilities.length; j++) {
        const a = availabilities[i];
        const b = availabilities[j];

        if (a.dayOfWeek === b.dayOfWeek) {
          if (
            (a.startTime <= b.startTime && a.endTime > b.startTime) ||
            (a.startTime < b.endTime && a.endTime >= b.endTime) ||
            (a.startTime >= b.startTime && a.endTime <= b.endTime)
          ) {
            throw new Error('Time slots overlap within the provided data');
          }
        }
      }
    }

    // Use transaction to replace all availability
    return await prisma.$transaction(async (tx) => {
      // Delete existing availability
      await tx.tutorAvailability.deleteMany({
        where: { tutorId }
      });

      // Create new availability slots
      const results: TutorAvailability[] = [];
      for (const availability of availabilities) {
        const created = await tx.tutorAvailability.create({
          data: {
            ...availability,
            tutorId
          }
        });
        results.push(created);
      }

      return results;
    });
  }

  /**
   * Validate time format (HH:MM)
   */
  private static isValidTimeFormat(time: string): boolean {
    const timeRegex = /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/;
    return timeRegex.test(time);
  }

  /**
   * Get formatted availability for display
   */
  static async getFormattedAvailability(tutorId: string): Promise<{
    [key: string]: { startTime: string; endTime: string; id: string }[]
  }> {
    const availability = await this.getTutorAvailability(tutorId);
    const dayNames = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
    
    const formatted: { [key: string]: { startTime: string; endTime: string; id: string }[] } = {};
    
    for (const slot of availability) {
      const dayName = dayNames[slot.dayOfWeek];
      if (!formatted[dayName]) {
        formatted[dayName] = [];
      }
      formatted[dayName].push({
        id: slot.id,
        startTime: slot.startTime,
        endTime: slot.endTime
      });
    }

    return formatted;
  }
}
