import { prisma } from '../lib/prisma';
import { TutorEducation } from '@prisma/client';

export interface CreateEducationData {
  tutorId: string;
  degree: string;
  fieldOfStudy: string;
  institution: string;
  startYear: number;
  endYear?: number;
  description?: string;
}

export interface UpdateEducationData {
  degree?: string;
  fieldOfStudy?: string;
  institution?: string;
  startYear?: number;
  endYear?: number;
  description?: string;
}

export class TutorEducationService {
  /**
   * Add education record for a tutor
   */
  static async addEducation(data: CreateEducationData): Promise<TutorEducation> {
    // Validate years
    const currentYear = new Date().getFullYear();
    
    if (data.startYear < 1900 || data.startYear > currentYear + 10) {
      throw new Error('Invalid start year');
    }

    if (data.endYear && (data.endYear < data.startYear || data.endYear > currentYear + 10)) {
      throw new Error('Invalid end year');
    }

    // Check if tutor exists
    const tutor = await prisma.tutorProfile.findUnique({
      where: { id: data.tutorId }
    });

    if (!tutor) {
      throw new Error('<PERSON><PERSON> profile not found');
    }

    return await prisma.tutorEducation.create({
      data
    });
  }

  /**
   * Get all education records for a tutor
   */
  static async getTutorEducation(tutorId: string): Promise<TutorEducation[]> {
    return await prisma.tutorEducation.findMany({
      where: { tutorId },
      orderBy: { startYear: 'desc' }
    });
  }

  /**
   * Get education record by ID
   */
  static async getEducationById(educationId: string): Promise<TutorEducation | null> {
    return await prisma.tutorEducation.findUnique({
      where: { id: educationId }
    });
  }

  /**
   * Update education record
   */
  static async updateEducation(
    educationId: string,
    data: UpdateEducationData
  ): Promise<TutorEducation> {
    const existing = await prisma.tutorEducation.findUnique({
      where: { id: educationId }
    });

    if (!existing) {
      throw new Error('Education record not found');
    }

    // Validate years if provided
    const currentYear = new Date().getFullYear();
    
    if (data.startYear && (data.startYear < 1900 || data.startYear > currentYear + 10)) {
      throw new Error('Invalid start year');
    }

    const startYear = data.startYear ?? existing.startYear;
    const endYear = data.endYear ?? existing.endYear;

    if (endYear && (endYear < startYear || endYear > currentYear + 10)) {
      throw new Error('Invalid end year');
    }

    return await prisma.tutorEducation.update({
      where: { id: educationId },
      data: {
        ...data,
        updatedAt: new Date()
      }
    });
  }

  /**
   * Delete education record
   */
  static async deleteEducation(educationId: string): Promise<void> {
    const existing = await prisma.tutorEducation.findUnique({
      where: { id: educationId }
    });

    if (!existing) {
      throw new Error('Education record not found');
    }

    await prisma.tutorEducation.delete({
      where: { id: educationId }
    });
  }

  /**
   * Bulk update education records for a tutor
   */
  static async bulkUpdateEducation(
    tutorId: string,
    educations: Omit<CreateEducationData, 'tutorId'>[]
  ): Promise<TutorEducation[]> {
    // Validate tutor exists
    const tutor = await prisma.tutorProfile.findUnique({
      where: { id: tutorId }
    });

    if (!tutor) {
      throw new Error('Tutor profile not found');
    }

    // Validate all education data
    const currentYear = new Date().getFullYear();
    
    for (const education of educations) {
      if (education.startYear < 1900 || education.startYear > currentYear + 10) {
        throw new Error('Invalid start year');
      }

      if (education.endYear && (education.endYear < education.startYear || education.endYear > currentYear + 10)) {
        throw new Error('Invalid end year');
      }
    }

    // Use transaction to replace all education records
    return await prisma.$transaction(async (tx) => {
      // Delete existing education records
      await tx.tutorEducation.deleteMany({
        where: { tutorId }
      });

      // Create new education records
      const results: TutorEducation[] = [];
      for (const education of educations) {
        const created = await tx.tutorEducation.create({
          data: {
            ...education,
            tutorId
          }
        });
        results.push(created);
      }

      return results;
    });
  }

  /**
   * Get education records by degree level
   */
  static async getEducationByDegree(tutorId: string, degree: string): Promise<TutorEducation[]> {
    return await prisma.tutorEducation.findMany({
      where: {
        tutorId,
        degree: {
          contains: degree,
          mode: 'insensitive'
        }
      },
      orderBy: { startYear: 'desc' }
    });
  }

  /**
   * Get education records by field of study
   */
  static async getEducationByField(tutorId: string, fieldOfStudy: string): Promise<TutorEducation[]> {
    return await prisma.tutorEducation.findMany({
      where: {
        tutorId,
        fieldOfStudy: {
          contains: fieldOfStudy,
          mode: 'insensitive'
        }
      },
      orderBy: { startYear: 'desc' }
    });
  }

  /**
   * Get highest education level for a tutor
   */
  static async getHighestEducation(tutorId: string): Promise<TutorEducation | null> {
    // Define degree hierarchy (higher index = higher degree)
    const degreeHierarchy = [
      'certificate',
      'diploma',
      'associate',
      'bachelor',
      'master',
      'doctorate',
      'phd'
    ];

    const educations = await this.getTutorEducation(tutorId);
    
    if (educations.length === 0) {
      return null;
    }

    // Find the highest degree
    let highest = educations[0];
    let highestIndex = -1;

    for (const education of educations) {
      const degreeIndex = degreeHierarchy.findIndex(degree => 
        education.degree.toLowerCase().includes(degree)
      );
      
      if (degreeIndex > highestIndex) {
        highestIndex = degreeIndex;
        highest = education;
      }
    }

    return highest;
  }

  /**
   * Validate education data
   */
  static validateEducationData(data: CreateEducationData | UpdateEducationData): string[] {
    const errors: string[] = [];
    const currentYear = new Date().getFullYear();

    if ('startYear' in data && data.startYear) {
      if (data.startYear < 1900 || data.startYear > currentYear + 10) {
        errors.push('Start year must be between 1900 and ' + (currentYear + 10));
      }
    }

    if ('endYear' in data && data.endYear) {
      if (data.endYear > currentYear + 10) {
        errors.push('End year cannot be more than 10 years in the future');
      }
      
      if ('startYear' in data && data.startYear && data.endYear < data.startYear) {
        errors.push('End year must be after start year');
      }
    }

    if ('degree' in data && data.degree && data.degree.trim().length < 2) {
      errors.push('Degree must be at least 2 characters long');
    }

    if ('fieldOfStudy' in data && data.fieldOfStudy && data.fieldOfStudy.trim().length < 2) {
      errors.push('Field of study must be at least 2 characters long');
    }

    if ('institution' in data && data.institution && data.institution.trim().length < 2) {
      errors.push('Institution must be at least 2 characters long');
    }

    return errors;
  }
}
