import { prisma } from '../lib/prisma';
import { Review } from '@prisma/client';

export interface CreateReviewData {
  appointmentId: string;
  tutorId: string;
  rating: number;
  comment?: string;
}

export interface UpdateReviewData {
  rating?: number;
  comment?: string;
}

export interface ReviewWithDetails extends Review {
  appointment: {
    id: string;
    startTime: Date;
    endTime: Date;
    meetingType: string;
    student: {
      id: string;
      name: string | null;
      email: string;
      avatar: string | null;
    };
  };
  tutor: {
    id: string;
    title: string | null;
    user: {
      id: string;
      name: string | null;
      email: string;
      avatar: string | null;
    };
  };
}

export interface TutorReviewStats {
  totalReviews: number;
  averageRating: number;
  ratingDistribution: {
    1: number;
    2: number;
    3: number;
    4: number;
    5: number;
  };
  recentReviews: ReviewWithDetails[];
}

export class ReviewService {
  /**
   * Create a review for a completed appointment
   */
  static async createReview(data: CreateReviewData, studentId: string): Promise<Review> {
    // Validate rating
    if (data.rating < 1 || data.rating > 5) {
      throw new Error('Rating must be between 1 and 5');
    }

    // Check if appointment exists and is completed
    const appointment = await prisma.appointment.findUnique({
      where: { id: data.appointmentId },
      include: {
        tutor: true,
        student: true
      }
    });

    if (!appointment) {
      throw new Error('Appointment not found');
    }

    if (appointment.status !== 'completed') {
      throw new Error('Can only review completed appointments');
    }

    if (appointment.studentId !== studentId) {
      throw new Error('You can only review your own appointments');
    }

    if (appointment.tutorId !== data.tutorId) {
      throw new Error('Tutor ID does not match appointment');
    }

    // Check if review already exists
    const existingReview = await prisma.review.findUnique({
      where: { appointmentId: data.appointmentId }
    });

    if (existingReview) {
      throw new Error('Review already exists for this appointment');
    }

    return await prisma.review.create({
      data: {
        appointmentId: data.appointmentId,
        tutorId: data.tutorId,
        rating: data.rating,
        comment: data.comment
      }
    });
  }

  /**
   * Get review by ID with details
   */
  static async getReviewById(reviewId: string): Promise<ReviewWithDetails | null> {
    return await prisma.review.findUnique({
      where: { id: reviewId },
      include: {
        appointment: {
          include: {
            student: {
              select: {
                id: true,
                name: true,
                email: true,
                avatar: true
              }
            }
          }
        },
        tutor: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true,
                avatar: true
              }
            }
          }
        }
      }
    });
  }

  /**
   * Get reviews for a tutor
   */
  static async getTutorReviews(
    tutorId: string,
    options: {
      page?: number;
      limit?: number;
      rating?: number;
    } = {}
  ): Promise<{
    reviews: ReviewWithDetails[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  }> {
    const { page = 1, limit = 10, rating } = options;
    const skip = (page - 1) * limit;

    const where: any = { tutorId };

    if (rating) {
      where.rating = rating;
    }

    const [reviews, total] = await Promise.all([
      prisma.review.findMany({
        where,
        skip,
        take: limit,
        include: {
          appointment: {
            include: {
              student: {
                select: {
                  id: true,
                  name: true,
                  email: true,
                  avatar: true
                }
              }
            }
          },
          tutor: {
            include: {
              user: {
                select: {
                  id: true,
                  name: true,
                  email: true,
                  avatar: true
                }
              }
            }
          }
        },
        orderBy: { createdAt: 'desc' }
      }),
      prisma.review.count({ where })
    ]);

    return {
      reviews,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit)
    };
  }

  /**
   * Get reviews by a student
   */
  static async getStudentReviews(
    studentId: string,
    options: {
      page?: number;
      limit?: number;
    } = {}
  ): Promise<{
    reviews: ReviewWithDetails[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  }> {
    const { page = 1, limit = 10 } = options;
    const skip = (page - 1) * limit;

    const [reviews, total] = await Promise.all([
      prisma.review.findMany({
        where: {
          appointment: {
            studentId
          }
        },
        skip,
        take: limit,
        include: {
          appointment: {
            include: {
              student: {
                select: {
                  id: true,
                  name: true,
                  email: true,
                  avatar: true
                }
              }
            }
          },
          tutor: {
            include: {
              user: {
                select: {
                  id: true,
                  name: true,
                  email: true,
                  avatar: true
                }
              }
            }
          }
        },
        orderBy: { createdAt: 'desc' }
      }),
      prisma.review.count({
        where: {
          appointment: {
            studentId
          }
        }
      })
    ]);

    return {
      reviews,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit)
    };
  }

  /**
   * Update a review
   */
  static async updateReview(
    reviewId: string,
    data: UpdateReviewData,
    studentId: string
  ): Promise<Review> {
    // Validate rating if provided
    if (data.rating && (data.rating < 1 || data.rating > 5)) {
      throw new Error('Rating must be between 1 and 5');
    }

    // Check if review exists and belongs to the student
    const existingReview = await prisma.review.findUnique({
      where: { id: reviewId },
      include: {
        appointment: true
      }
    });

    if (!existingReview) {
      throw new Error('Review not found');
    }

    if (existingReview.appointment.studentId !== studentId) {
      throw new Error('You can only update your own reviews');
    }

    return await prisma.review.update({
      where: { id: reviewId },
      data: {
        ...data,
        updatedAt: new Date()
      }
    });
  }

  /**
   * Delete a review
   */
  static async deleteReview(reviewId: string, studentId: string): Promise<void> {
    // Check if review exists and belongs to the student
    const existingReview = await prisma.review.findUnique({
      where: { id: reviewId },
      include: {
        appointment: true
      }
    });

    if (!existingReview) {
      throw new Error('Review not found');
    }

    if (existingReview.appointment.studentId !== studentId) {
      throw new Error('You can only delete your own reviews');
    }

    await prisma.review.delete({
      where: { id: reviewId }
    });
  }

  /**
   * Get tutor review statistics
   */
  static async getTutorReviewStats(tutorId: string): Promise<TutorReviewStats> {
    // Get all reviews for the tutor
    const reviews = await prisma.review.findMany({
      where: { tutorId },
      include: {
        appointment: {
          include: {
            student: {
              select: {
                id: true,
                name: true,
                email: true,
                avatar: true
              }
            }
          }
        },
        tutor: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true,
                avatar: true
              }
            }
          }
        }
      },
      orderBy: { createdAt: 'desc' }
    });

    const totalReviews = reviews.length;
    const averageRating = totalReviews > 0
      ? reviews.reduce((sum, review) => sum + review.rating, 0) / totalReviews
      : 0;

    // Calculate rating distribution
    const ratingDistribution = {
      1: 0,
      2: 0,
      3: 0,
      4: 0,
      5: 0
    };

    reviews.forEach(review => {
      ratingDistribution[review.rating as keyof typeof ratingDistribution]++;
    });

    // Get recent reviews (last 5)
    const recentReviews = reviews.slice(0, 5);

    return {
      totalReviews,
      averageRating: Math.round(averageRating * 10) / 10, // Round to 1 decimal place
      ratingDistribution,
      recentReviews
    };
  }

  /**
   * Get review by appointment ID
   */
  static async getReviewByAppointmentId(appointmentId: string): Promise<ReviewWithDetails | null> {
    return await prisma.review.findUnique({
      where: { appointmentId },
      include: {
        appointment: {
          include: {
            student: {
              select: {
                id: true,
                name: true,
                email: true,
                avatar: true
              }
            }
          }
        },
        tutor: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true,
                avatar: true
              }
            }
          }
        }
      }
    });
  }

  /**
   * Get all reviews (admin only)
   */
  static async getAllReviews(
    options: {
      page?: number;
      limit?: number;
      rating?: number;
      tutorId?: string;
      studentId?: string;
    } = {}
  ): Promise<{
    reviews: ReviewWithDetails[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  }> {
    const { page = 1, limit = 10, rating, tutorId, studentId } = options;
    const skip = (page - 1) * limit;

    const where: any = {};

    if (rating) {
      where.rating = rating;
    }

    if (tutorId) {
      where.tutorId = tutorId;
    }

    if (studentId) {
      where.appointment = {
        studentId
      };
    }

    const [reviews, total] = await Promise.all([
      prisma.review.findMany({
        where,
        skip,
        take: limit,
        include: {
          appointment: {
            include: {
              student: {
                select: {
                  id: true,
                  name: true,
                  email: true,
                  avatar: true
                }
              }
            }
          },
          tutor: {
            include: {
              user: {
                select: {
                  id: true,
                  name: true,
                  email: true,
                  avatar: true
                }
              }
            }
          }
        },
        orderBy: { createdAt: 'desc' }
      }),
      prisma.review.count({ where })
    ]);

    return {
      reviews,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit)
    };
  }

  /**
   * Get review statistics (admin only)
   */
  static async getReviewStatistics(): Promise<{
    totalReviews: number;
    averageRating: number;
    ratingDistribution: {
      1: number;
      2: number;
      3: number;
      4: number;
      5: number;
    };
    reviewsThisMonth: number;
    reviewsLastMonth: number;
  }> {
    const now = new Date();
    const startOfThisMonth = new Date(now.getFullYear(), now.getMonth(), 1);
    const startOfLastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1);
    const endOfLastMonth = new Date(now.getFullYear(), now.getMonth(), 0);

    const [
      allReviews,
      reviewsThisMonth,
      reviewsLastMonth
    ] = await Promise.all([
      prisma.review.findMany({
        select: { rating: true }
      }),
      prisma.review.count({
        where: {
          createdAt: {
            gte: startOfThisMonth
          }
        }
      }),
      prisma.review.count({
        where: {
          createdAt: {
            gte: startOfLastMonth,
            lte: endOfLastMonth
          }
        }
      })
    ]);

    const totalReviews = allReviews.length;
    const averageRating = totalReviews > 0
      ? allReviews.reduce((sum, review) => sum + review.rating, 0) / totalReviews
      : 0;

    // Calculate rating distribution
    const ratingDistribution = {
      1: 0,
      2: 0,
      3: 0,
      4: 0,
      5: 0
    };

    allReviews.forEach(review => {
      ratingDistribution[review.rating as keyof typeof ratingDistribution]++;
    });

    return {
      totalReviews,
      averageRating: Math.round(averageRating * 10) / 10,
      ratingDistribution,
      reviewsThisMonth,
      reviewsLastMonth
    };
  }
}
